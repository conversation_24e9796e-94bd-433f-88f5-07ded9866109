import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Star, ChevronRight, Edit } from "lucide-react";
import { Header } from "@/components/Header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import salon1 from "@/assets/salon-1.jpg";
import salon2 from "@/assets/salon-2.jpg";
import salon3 from "@/assets/salon-3.jpg";
import salon4 from "@/assets/salon-4.jpg";
import salon5 from "@/assets/salon-5.jpg";
import salon6 from "@/assets/salon-6.jpg";

const salons = [
  {
    id: "1",
    name: "The Hair Lounge",
    location: "San Francisco",
    distance: "Downtown",
    rating: 4.8,
    reviews: 120,
    price: 50,
    description: "Welcome to The Hair Lounge, a premier salon in the heart of San Francisco. Our expert stylists offer a range of services from haircuts and coloring to styling and treatments. We use only the best products to ensure your hair looks and feels its best. Book your appointment today and experience the difference!",
    images: [salon1, salon2, salon3, salon4, salon5],
    services: [
      { name: "Haircut", price: 50 },
      { name: "Hair Coloring", price: 120 },
      { name: "<PERSON>yl<PERSON>", price: 80 },
      { name: "Treatment", price: 60 }
    ],
    stylists: [
      { name: "<PERSON> <PERSON>", role: "Expert Stylist", image: salon1 },
      { name: "Sophia <PERSON>", role: "Color Specialist", image: salon2 },
      { name: "Olivia Hayes", role: "Styling Pro", image: salon3 }
    ],
    ratings: {
      cleanliness: 4.9,
      accuracy: 4.8,
      communication: 4.9,
      location: 5.0,
      value: 4.7
    }
  },
  {
    id: "2",
    name: "Urban Beauty Lounge",
    location: "Mission District",
    distance: "5 miles away",
    rating: 4.9,
    reviews: 89,
    price: 65,
    description: "Urban Beauty Lounge brings modern styling to the Mission District. Our team of experienced professionals specializes in contemporary cuts and vibrant color treatments.",
    images: [salon2, salon3, salon4, salon5, salon6],
    services: [
      { name: "Haircut", price: 65 },
      { name: "Hair Coloring", price: 130 },
      { name: "Styling", price: 85 },
      { name: "Treatment", price: 70 }
    ],
    stylists: [
      { name: "Marcus Johnson", role: "Senior Stylist", image: salon2 },
      { name: "Luna Rodriguez", role: "Color Expert", image: salon3 },
      { name: "Alex Chen", role: "Style Director", image: salon4 }
    ],
    ratings: {
      cleanliness: 4.8,
      accuracy: 4.9,
      communication: 4.8,
      location: 4.9,
      value: 4.8
    }
  }
];

const SalonDetails = () => {
  const { id } = useParams();
  const salon = salons.find(s => s.id === id) || salons[0];

  return (
    <div className="min-h-screen bg-glamspot-neutral-50">
      <Header />
      
      <main className="px-40 flex flex-1 justify-center py-10">
        <div className="flex flex-col max-w-[960px] flex-1 gap-8">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-glamspot-neutral-500">
            <Link to="/" className="hover:text-glamspot-primary hover:underline">
              California
            </Link>
            <ChevronRight className="w-4 h-4" />
            <span className="font-medium text-glamspot-neutral-800">San Francisco</span>
          </div>

          {/* Title and Rating */}
          <div className="flex flex-wrap justify-between gap-3">
            <div className="flex flex-col gap-2">
              <h1 className="text-4xl font-bold text-glamspot-neutral-900">{salon.name}</h1>
              <div className="flex items-center gap-2 text-glamspot-neutral-600">
                <Star className="w-5 h-5 text-yellow-500 fill-current" />
                <span className="font-semibold">{salon.rating}</span>
                <span className="text-glamspot-neutral-400">·</span>
                <a href="#reviews" className="underline hover:text-glamspot-primary">
                  {salon.reviews} reviews
                </a>
                <span className="text-glamspot-neutral-400">·</span>
                <span>{salon.location}, California</span>
              </div>
            </div>
          </div>

          {/* Image Gallery */}
          <div className="grid grid-cols-4 gap-2 h-96 overflow-hidden rounded-2xl">
            <div className="col-span-2 row-span-2">
              <img 
                className="w-full h-full object-cover" 
                src={salon.images[0]} 
                alt={salon.name}
              />
            </div>
            {salon.images.slice(1, 5).map((image, index) => (
              <img 
                key={index}
                className="w-full h-full object-cover" 
                src={image} 
                alt={`${salon.name} interior ${index + 2}`}
              />
            ))}
          </div>

          <div className="grid grid-cols-3 gap-16">
            {/* Main Content */}
            <div className="col-span-2 space-y-8">
              {/* About Section */}
              <section>
                <h2 className="text-2xl font-bold mb-4 text-glamspot-neutral-900">About {salon.name}</h2>
                <p className="text-glamspot-neutral-600">{salon.description}</p>
              </section>

              <div className="border-t border-glamspot-neutral-200"></div>

              {/* Services Section */}
              <section>
                <h2 className="text-2xl font-bold mb-4 text-glamspot-neutral-900">What this place offers</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <span className="material-symbols-outlined text-glamspot-neutral-700">content_cut</span>
                    <p className="text-glamspot-neutral-800">Haircuts</p>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="material-symbols-outlined text-glamspot-neutral-700">palette</span>
                    <p className="text-glamspot-neutral-800">Hair Coloring</p>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="material-symbols-outlined text-glamspot-neutral-700">auto_fix_high</span>
                    <p className="text-glamspot-neutral-800">Styling</p>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="material-symbols-outlined text-glamspot-neutral-700">spa</span>
                    <p className="text-glamspot-neutral-800">Treatments</p>
                  </div>
                </div>
              </section>

              <div className="border-t border-glamspot-neutral-200"></div>

              {/* Stylists Section */}
              <section>
                <h2 className="text-2xl font-bold mb-4 text-glamspot-neutral-900">Stylists</h2>
                <div className="grid grid-cols-3 gap-8">
                  {salon.stylists.map((stylist, index) => (
                    <div key={index} className="text-center">
                      <img 
                        className="w-24 h-24 rounded-full mx-auto mb-2 object-cover" 
                        src={stylist.image} 
                        alt={stylist.name}
                      />
                      <p className="font-semibold text-glamspot-neutral-900">{stylist.name}</p>
                      <p className="text-sm text-glamspot-neutral-500">{stylist.role}</p>
                    </div>
                  ))}
                </div>
              </section>

              <div className="border-t border-glamspot-neutral-200"></div>

              {/* Location Section */}
              <section>
                <h2 className="text-2xl font-bold mb-4 text-glamspot-neutral-900">Where you'll be</h2>
                <img 
                  className="w-full h-80 object-cover rounded-2xl" 
                  src={salon.images[0]} 
                  alt="Location map"
                />
              </section>
            </div>

            {/* Booking Sidebar */}
            <div className="col-span-1">
              <div className="sticky top-28 p-6 rounded-2xl border border-glamspot-neutral-200 shadow-xl bg-white space-y-6">
                <div className="flex justify-between items-baseline">
                  <p>
                    <span className="text-2xl font-bold text-glamspot-neutral-900">${salon.price}</span> 
                    <span className="text-glamspot-neutral-600"> / service</span>
                  </p>
                  <div className="flex items-center gap-1 text-sm">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="font-semibold text-glamspot-neutral-900">{salon.rating}</span>
                    <span className="text-glamspot-neutral-500">({salon.reviews})</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-px border border-glamspot-neutral-300 rounded-lg">
                  <div className="p-3">
                    <label className="block text-xs font-bold uppercase text-glamspot-neutral-500" htmlFor="checkin">
                      Check-in
                    </label>
                    <input 
                      className="w-full border-none p-0 text-sm focus:ring-0 bg-transparent text-glamspot-neutral-800" 
                      id="checkin" 
                      type="text" 
                      defaultValue="06/05/2024"
                    />
                  </div>
                  <div className="p-3 border-l border-glamspot-neutral-300">
                    <label className="block text-xs font-bold uppercase text-glamspot-neutral-500" htmlFor="checkout">
                      Check-out
                    </label>
                    <input 
                      className="w-full border-none p-0 text-sm focus:ring-0 bg-transparent text-glamspot-neutral-800" 
                      id="checkout" 
                      type="text" 
                      defaultValue="07/07/2024"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-glamspot-neutral-900">Available Services</h3>
                  <div className="space-y-2 text-sm">
                    {salon.services.map((service, index) => (
                      <div key={index} className="flex justify-between">
                        <label className="flex items-center gap-2 cursor-pointer" htmlFor={service.name.toLowerCase()}>
                          <Checkbox 
                            id={service.name.toLowerCase()}
                            className="border-glamspot-neutral-300"
                          />
                          <span className="text-glamspot-neutral-800">{service.name}</span>
                        </label>
                        <span className="text-glamspot-neutral-800">${service.price}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <Button className="w-full bg-glamspot-primary hover:bg-glamspot-primary-dark text-white font-bold py-3">
                  Book Now
                </Button>
                
                <div className="text-center text-sm text-glamspot-neutral-500">
                  You won't be charged yet
                </div>

                <div className="border-t border-glamspot-neutral-200 pt-4 space-y-3 text-sm">
                  <div className="flex justify-between text-glamspot-neutral-800">
                    <span>Haircut (${salon.services[0].price})</span>
                    <span>${salon.services[0].price}</span>
                  </div>
                  <div className="flex justify-between text-glamspot-neutral-800">
                    <span>Styling (${salon.services[2].price})</span>
                    <span>${salon.services[2].price}</span>
                  </div>
                  <div className="flex justify-between font-bold border-t border-glamspot-neutral-200 pt-3 mt-3 text-glamspot-neutral-900">
                    <span>Total</span>
                    <span>${salon.services[0].price + salon.services[2].price}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Reviews Section */}
          <div className="border-t border-glamspot-neutral-200"></div>
          
          <section className="space-y-8" id="reviews">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold flex items-center gap-2 text-glamspot-neutral-900">
                <Star className="w-6 h-6 text-yellow-500 fill-current" />
                {salon.rating} · {salon.reviews} reviews
              </h2>
              <Button variant="outline" className="flex items-center gap-2">
                <Edit className="w-4 h-4" />
                Leave a review
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-8 text-sm mb-8">
              <div className="space-y-4">
                {Object.entries(salon.ratings).slice(0, 3).map(([key, value]) => (
                  <div key={key} className="flex justify-between items-center">
                    <span className="capitalize text-glamspot-neutral-800">{key}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 h-1 bg-glamspot-neutral-200 rounded-full">
                        <div 
                          className="h-1 bg-glamspot-neutral-800 rounded-full" 
                          style={{ width: `${(value / 5) * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-glamspot-neutral-800">{value}</span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="space-y-4">
                {Object.entries(salon.ratings).slice(3).map(([key, value]) => (
                  <div key={key} className="flex justify-between items-center">
                    <span className="capitalize text-glamspot-neutral-800">{key}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 h-1 bg-glamspot-neutral-200 rounded-full">
                        <div 
                          className="h-1 bg-glamspot-neutral-800 rounded-full" 
                          style={{ width: `${(value / 5) * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-glamspot-neutral-800">{value}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Sample Reviews */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-8">
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <img 
                    className="w-10 h-10 rounded-full object-cover" 
                    src={salon1} 
                    alt="Ava Harper"
                  />
                  <div>
                    <p className="font-semibold text-glamspot-neutral-900">Ava Harper</p>
                    <p className="text-sm text-glamspot-neutral-500">May 2023</p>
                  </div>
                </div>
                <p className="text-glamspot-neutral-600">
                  Emily is an amazing stylist! She really listened to what I wanted and gave me the perfect haircut. The salon has a great atmosphere, and everyone was so friendly. I highly recommend {salon.name}!
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <img 
                    className="w-10 h-10 rounded-full object-cover" 
                    src={salon2} 
                    alt="Chloe Foster"
                  />
                  <div>
                    <p className="font-semibold text-glamspot-neutral-900">Chloe Foster</p>
                    <p className="text-sm text-glamspot-neutral-500">April 2023</p>
                  </div>
                </div>
                <p className="text-glamspot-neutral-600">
                  I had a great experience at {salon.name}. Sophia did a fantastic job with my color, and I love the result. The salon is clean and well-maintained, but the service could be a bit faster.
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <img 
                    className="w-10 h-10 rounded-full object-cover" 
                    src={salon3} 
                    alt="Isabella Reed"
                  />
                  <div>
                    <p className="font-semibold text-glamspot-neutral-900">Isabella Reed</p>
                    <p className="text-sm text-glamspot-neutral-500">March 2023</p>
                  </div>
                </div>
                <p className="text-glamspot-neutral-600">
                  Olivia is a true professional. She styled my hair for a special event, and it looked absolutely stunning. The salon is beautiful, and the staff is very talented. I will definitely be back!
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <img 
                    className="w-10 h-10 rounded-full object-cover" 
                    src={salon4} 
                    alt="James Anderson"
                  />
                  <div>
                    <p className="font-semibold text-glamspot-neutral-900">James Anderson</p>
                    <p className="text-sm text-glamspot-neutral-500">February 2023</p>
                  </div>
                </div>
                <p className="text-glamspot-neutral-600">
                  Great service and atmosphere. The stylists are top-notch. I've been coming here for years and I'm always happy with my haircut. Highly recommended.
                </p>
              </div>
            </div>

            <div className="mt-8">
              <Button variant="outline">
                Show all {salon.reviews} reviews
              </Button>
            </div>
          </section>
        </div>
      </main>
    </div>
  );
};

export default SalonDetails;