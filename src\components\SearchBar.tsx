import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

export const SearchBar = () => {
  return (
    <div className="flex items-center border border-glamspot-neutral-200 rounded-full shadow-sm hover:shadow-md transition-shadow bg-white">
      <button className="flex-1 px-6 py-3 text-left text-glamspot-neutral-700 hover:bg-glamspot-neutral-50 rounded-l-full transition-colors">
        <div className="text-sm font-medium">Anywhere</div>
      </button>
      
      <div className="w-px h-8 bg-glamspot-neutral-200" />
      
      <button className="flex-1 px-6 py-3 text-left text-glamspot-neutral-700 hover:bg-glamspot-neutral-50 transition-colors">
        <div className="text-sm font-medium">Any week</div>
      </button>
      
      <div className="w-px h-8 bg-glamspot-neutral-200" />
      
      <button className="flex-1 px-6 py-3 text-left text-glamspot-neutral-500 hover:bg-glamspot-neutral-50 transition-colors">
        <div className="text-sm">Add guests</div>
      </button>
      
      <Button 
        size="icon" 
        className="m-2 rounded-full bg-glamspot-primary hover:bg-glamspot-primary-dark text-white"
      >
        <Search className="w-4 h-4" />
      </Button>
    </div>
  );
};