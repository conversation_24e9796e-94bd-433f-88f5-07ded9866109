import { useState } from "react";
import { Button } from "@/components/ui/button";
import { SearchBar } from "./SearchBar";
import { Heart, Globe, Menu, User } from "lucide-react";

export const Header = () => {
  return (
    <header className="sticky top-0 z-50 bg-white border-b border-glamspot-neutral-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex items-center justify-between py-4 gap-8">
          {/* Logo */}
          <div className="flex items-center gap-2 text-glamspot-primary">
            <div className="w-8 h-8 bg-glamspot-primary rounded-lg flex items-center justify-center">
              <Heart className="w-5 h-5 text-white fill-current" />
            </div>
            <h1 className="text-2xl font-bold tracking-tight hidden sm:block">GlamSpot</h1>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-2xl">
            <SearchBar />
          </div>

          {/* Right Navigation */}
          <div className="flex items-center gap-4">
            <Button variant="ghost" className="hidden lg:flex text-glamspot-neutral-700 hover:text-glamspot-primary">
              List your space
            </Button>
            
            <Button variant="ghost" size="icon" className="text-glamspot-neutral-700 hover:text-glamspot-primary">
              <Globe className="w-5 h-5" />
            </Button>

            <div className="flex items-center gap-2 border border-glamspot-neutral-200 rounded-full p-2 hover:shadow-md transition-shadow">
              <Menu className="w-4 h-4 text-glamspot-neutral-700" />
              <div className="w-8 h-8 bg-glamspot-neutral-500 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};