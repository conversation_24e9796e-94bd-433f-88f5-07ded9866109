import { Header } from "@/components/Header";
import { StudioCard } from "@/components/StudioCard";
import { FilterControls } from "@/components/FilterControls";
import salon1 from "@/assets/salon-1.jpg";
import salon2 from "@/assets/salon-2.jpg";
import salon3 from "@/assets/salon-3.jpg";
import salon4 from "@/assets/salon-4.jpg";
import salon5 from "@/assets/salon-5.jpg";
import salon6 from "@/assets/salon-6.jpg";

const studios = [
  {
    id: "1",
    name: "Chic Hair Studio",
    location: "Downtown",
    distance: "10 miles away",
    rating: 4.8,
    price: 50,
    image: salon1,
  },
  {
    id: "2", 
    name: "Urban Beauty Lounge",
    location: "Mission District",
    distance: "5 miles away",
    rating: 4.9,
    price: 65,
    image: salon2,
  },
  {
    id: "3",
    name: "The Style Loft",
    location: "Marina",
    distance: "8 miles away", 
    rating: 4.7,
    price: 55,
    image: salon3,
  },
  {
    id: "4",
    name: "Glamour Zone",
    location: "SoMa",
    distance: "3 miles away",
    rating: 4.6,
    price: 60,
    image: salon4,
  },
  {
    id: "5",
    name: "Hair Haven",
    location: "Richmond",
    distance: "12 miles away",
    rating: 4.5,
    price: 45,
    image: salon5,
  },
  {
    id: "6",
    name: "The Beauty Spot",
    location: "North Beach",
    distance: "7 miles away",
    rating: 4.9,
    price: 70,
    image: salon6,
  },
  {
    id: "7",
    name: "Style & Grace Salon",
    location: "Sunset",
    distance: "15 miles away",
    rating: 4.8,
    price: 60,
    image: salon1,
  },
  {
    id: "8",
    name: "The Hair Studio",
    location: "Financial District",
    distance: "2 miles away",
    rating: 4.7,
    price: 75,
    image: salon2,
  },
];

const Index = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">
            100+ places in San Francisco
          </h1>
          <FilterControls />
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {studios.map((studio) => (
            <StudioCard key={studio.id} {...studio} />
          ))}
        </div>
      </main>
    </div>
  );
};

export default Index;
