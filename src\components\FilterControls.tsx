import { Button } from "@/components/ui/button";
import { SlidersHorizontal, Map } from "lucide-react";

export const FilterControls = () => {
  return (
    <div className="flex items-center gap-4">
      <Button 
        variant="outline" 
        className="flex items-center gap-2 border-glamspot-neutral-200 text-glamspot-neutral-700 hover:bg-glamspot-neutral-50"
      >
        <SlidersHorizontal className="w-4 h-4" />
        Filters
      </Button>
      
      <Button 
        variant="outline"
        className="flex items-center gap-2 border-glamspot-neutral-200 text-glamspot-neutral-700 hover:bg-glamspot-neutral-50"
      >
        <Map className="w-4 h-4" />
        Show map
      </Button>
    </div>
  );
};